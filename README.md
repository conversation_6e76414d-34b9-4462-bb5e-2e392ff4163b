# LLIE 低光图像增强项目

[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://www.python.org/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0%2B-red.svg)](https://pytorch.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

一个现代化的低光图像增强深度学习项目，基于LLIE架构实现。

## 🌟 项目特色

### 🏗️ 现代化架构
- **模块化设计**: 清晰的代码组织和组件分离
- **注册表系统**: 灵活的模型、损失函数、数据集注册机制
- **类型安全**: 完整的类型提示和错误处理
- **配置驱动**: 基于Hydra的层次化配置管理

### 🚀 先进功能
- **混合精度训练**: 加速训练并节省显存
- **实验跟踪**: 集成Weights & Biases进行全面的实验记录
- **智能早停**: 基于验证指标的自动早停机制
- **模型EMA**: 指数移动平均提高模型稳定性
- **梯度裁剪**: 防止梯度爆炸，确保训练稳定

### 📊 丰富的评估指标
- **PSNR**: 峰值信噪比
- **SSIM**: 结构相似性指数
- **MAE**: 平均绝对误差
- **LPIPS**: 感知损失（可选）

### 🎨 美观的用户界面
- **Rich进度条**: 实时显示训练进度和状态
- **彩色日志**: 清晰的日志输出和错误提示
- **实时监控**: 训练指标的实时可视化

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建conda环境
conda create -n llie python=3.9 -y
conda activate llie

# 安装PyTorch (GPU版本)
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 安装项目依赖
pip install -r requirements.txt
pip install -e .
```

详细的环境配置请参考：[环境配置指南](docs/environment_setup.md)

### 2. 数据准备

```bash
# 创建数据目录
mkdir -p data/LOLv2/Real_captured/{Train,Test}/{Low,Normal}

# 下载LOL数据集并解压到相应目录
```

### 3. 开始训练

```bash
# 使用默认配置训练
python run.py

# 自定义配置训练
python run.py trainer.batch_size=8 trainer.max_epochs=50

# 使用不同的模型配置
python run.py model.architecture.s_nf=64 model.architecture.num_blocks=8
```

### 4. 模型评估

```bash
# 评估训练好的模型
python run.py task=evaluate evaluation.checkpoint_path=outputs/checkpoints/best_model.pth
```

### 5. 图像推理

```bash
# 对单张图像进行推理
python run.py task=inference \
  inference.checkpoint_path=outputs/checkpoints/best_model.pth \
  inference.input_path=path/to/your/image.jpg
```

## 📁 项目结构

```
LLIE/
├── src/llie/                 # 主要源代码
│   ├── models/              # 模型实现
│   │   ├── llie.py         # LLIE主模型
│   │   ├── components/      # 模型组件
│   │   └── base_model.py    # 基础模型类
│   ├── data/                # 数据加载和变换
│   │   ├── dataset.py       # 数据集类
│   │   └── transforms.py    # 数据变换
│   ├── engine/              # 训练引擎
│   │   └── trainer.py       # 现代化训练器
│   ├── tasks/               # 任务实现
│   │   ├── train.py         # 训练任务
│   │   ├── evaluate.py      # 评估任务
│   │   └── inference.py     # 推理任务
│   ├── metrics/             # 评估指标
│   └── utils/               # 工具函数和注册表
├── configs/                 # Hydra配置文件
│   ├── config.yaml         # 主配置文件
│   ├── model/              # 模型配置
│   ├── dataset/            # 数据集配置
│   ├── trainer/            # 训练器配置
│   └── task/               # 任务配置
├── docs/                   # 项目文档
│   ├── README.md           # 文档概览
│   ├── configuration_guide.md  # 配置指南
│   └── environment_setup.md    # 环境配置
├── notebooks/              # Jupyter笔记本
├── outputs/                # 训练输出
├── data/                   # 数据集目录
└── run.py                  # 主运行脚本
```

## 🔧 配置系统

项目使用Hydra进行配置管理，所有配置文件位于`configs/`目录：

### 主要配置文件
- **config.yaml**: 主配置文件，组合其他配置组件
- **model/llie.yaml**: LLIE模型架构配置
- **dataset/LOLv2_Real.yaml**: LOL数据集配置
- **trainer/default_trainer.yaml**: 训练器参数配置
- **task/**: 任务特定配置（train/evaluate/inference）

### 配置示例

```bash
# 修改批次大小和学习率
python run.py trainer.batch_size=16 trainer.optimizer.lr=0.002

# 使用不同的模型配置
python run.py model.architecture.s_nf=32 model.architecture.num_blocks=4

# 启用混合精度训练
python run.py trainer.use_amp=true

# 超参数扫描
python run.py --multirun trainer.optimizer.lr=0.001,0.002,0.005
```

详细的配置说明请参考：[配置指南](docs/configuration_guide.md)

## 🎯 模型架构

### LLIE: 低光图像增强网络

LLIE采用四级处理架构：

1. **亮度图处理**: 提取和处理图像的亮度信息，为后续处理提供照明指导
2. **傅里叶域增强**: 在频域进行全局特征处理，利用FFT进行跨模态特征融合
3. **空间-频率双重处理**: 结合空间域和频域特征，使用FFC块进行特征融合
4. **最终重建**: 生成增强后的图像，保持图像细节和颜色一致性

### 关键特性
- **多尺度特征处理**: 适应不同光照条件
- **频域和空间域协同**: 全面的特征表示
- **端到端训练**: 无需复杂的预处理步骤
- **残差连接**: 保持图像细节信息

## 📊 实验跟踪

项目集成了Weights & Biases进行实验跟踪：

```yaml
# 在config.yaml中配置W&B
wandb:
  project: low-light-enhancement
  entity: your_username
  tags: [dmfourllie, low-light, enhancement]
  mode: online
```

跟踪的内容包括：
- 训练和验证损失
- 评估指标（PSNR、SSIM、MAE）
- 学习率变化
- 模型参数
- 样本图像对比

## 🛠️ 开发指南

### 添加新模型

1. 在`src/llie/models/`中创建新模型文件
2. 继承`BaseArchitecture`类
3. 使用`@register_model`装饰器注册
4. 在`configs/model/`中添加配置文件

```python
from ..utils.registry import register_model
from .base_model import BaseArchitecture

@register_model("YourModel")
class YourModel(BaseArchitecture):
    def __init__(self, **kwargs):
        super().__init__()
        # 模型实现
    
    def forward(self, x):
        # 前向传播
        return enhanced_image
```

## 📈 性能优化

### 训练加速技巧

```bash
# 启用混合精度训练
python run.py trainer.use_amp=true

# 增加数据加载进程数
python run.py num_workers=8

# 使用更大的批次大小（如果显存允许）
python run.py trainer.batch_size=32
```

### 显存优化

```bash
# 减小批次大小
python run.py trainer.batch_size=4

# 减小模型尺寸
python run.py model.architecture.s_nf=16 model.architecture.num_blocks=4
```

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- LOL数据集的作者
- PyTorch团队
- Hydra配置框架
- Weights & Biases实验跟踪平台
- 所有开源贡献者

## 📞 联系方式

如果您有任何问题或建议，请：
1. 查看[文档](docs/)
2. 提交GitHub Issue
3. 参考[配置指南](docs/configuration_guide.md)

---

**记住**: 在运行任何命令前，请确保激活conda环境：`conda activate llie`
